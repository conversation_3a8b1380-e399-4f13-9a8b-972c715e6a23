import { useEffect, useState } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import { Layout } from './components/Layout';
import { ESealTable } from './components/ESealTable';
import { <PERSON><PERSON><PERSON>Kepabeaan } from './components/DokumenKepabeaan';
import { UpdatePosisi } from './components/superadmin/UpdatePosisi';
import { UpdateStatusDevice } from './components/superadmin/UpdateStatusDevice';
import { TrackingStart } from './components/superadmin/TrackingStart';
import { TrackingStop } from './components/superadmin/TrackingStop';
import { TrackingStatus } from './components/superadmin/TrackingStatus';
import { Logs } from './components/superadmin/Logs';

// Placeholder components for other pages
function UpdatePosisi() {
  const [search, setSearch] = useState('');
  const [entriesPerPage, setEntriesPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Mock data sesuai dengan struktur table yang diminta
  const mockData = [
    {
      id: 1,
      perusahaan: "Perusahaan A",
      address: "Jalan Pantura, Kendal, Jawa Tengah",
      posisiAltitude: "6363737373737",
      dayaBaterai: "15000",
      dayaAki: "350000",
      event: "UNLOCK",
      idVendor: "464641LU3",
      kecepatan: "0",
      posisiLatitude: "6427373737",
      posisiLongitude: "2734636364",
      provinsi: "Jawa Tengah",
      suhu: "Rendah",
      noIMEI: "862048059607602",
      noESeal: "3M8M1S5"
    }
  ];

  const filteredData = mockData.filter(item =>
    item.perusahaan.toLowerCase().includes(search.toLowerCase()) ||
    item.address.toLowerCase().includes(search.toLowerCase()) ||
    item.event.toLowerCase().includes(search.toLowerCase()) ||
    item.idVendor.toLowerCase().includes(search.toLowerCase())
  );

  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / entriesPerPage);
  const startEntry = (currentPage - 1) * entriesPerPage + 1;
  const endEntry = Math.min(currentPage * entriesPerPage, totalEntries);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Update Posisi</h1>
        <div className="w-16 h-1 bg-blue-600"></div>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Show</span>
          <select
            value={entriesPerPage}
            onChange={(e) => {
              setEntriesPerPage(parseInt(e.target.value));
              setCurrentPage(1);
            }}
            className="border border-gray-300 rounded px-2 py-1 text-sm"
          >
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
          </select>
          <span className="text-sm text-gray-700">entries per page</span>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Cari</span>
          <div className="relative">
            <input
              type="text"
              placeholder="Search..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="border border-gray-300 rounded px-3 py-1 pl-8 text-sm w-64"
            />
            <svg className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
        <div className="overflow-x-auto">
          <table className="w-full caption-bottom text-sm">
            <thead className="border-b">
              <tr className="bg-slate-50 border-b-2 border-slate-200">
                <th className="min-w-[50px] font-semibold text-slate-700 text-center p-2">No</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Perusahaan</th>
                <th className="min-w-[200px] font-semibold text-slate-700 text-center p-2">Address</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Posisi Altitude</th>
                <th className="min-w-[100px] font-semibold text-slate-700 text-center p-2">Daya Baterai</th>
                <th className="min-w-[100px] font-semibold text-slate-700 text-center p-2">Daya Aki</th>
                <th className="min-w-[80px] font-semibold text-slate-700 text-center p-2">Event</th>
                <th className="min-w-[100px] font-semibold text-slate-700 text-center p-2">ID Vendor</th>
                <th className="min-w-[100px] font-semibold text-slate-700 text-center p-2">Kecepatan Kontainer</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Posisi Latitude</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Posisi Longitude</th>
                <th className="min-w-[100px] font-semibold text-slate-700 text-center p-2">Provinsi</th>
                <th className="min-w-[80px] font-semibold text-slate-700 text-center p-2">Suhu</th>
                <th className="min-w-[150px] font-semibold text-slate-700 text-center p-2">No IMEI</th>
                <th className="min-w-[100px] font-semibold text-slate-700 text-center p-2">No E-Seal</th>
              </tr>
            </thead>
            <tbody>
              {filteredData.length === 0 ? (
                <tr>
                  <td colSpan={15} className="text-center py-8 text-gray-500">
                    {search ? 'Data tidak ditemukan' : 'Data Masih Kosong'}
                  </td>
                </tr>
              ) : (
                filteredData.map((item, index) => (
                  <tr key={item.id} className="hover:bg-slate-50 transition-colors border-b">
                    <td className="text-center p-2 font-medium">{startEntry + index}.</td>
                    <td className="text-center p-2">{item.perusahaan}</td>
                    <td className="text-center p-2">{item.address}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.posisiAltitude}</td>
                    <td className="text-center p-2">{item.dayaBaterai}</td>
                    <td className="text-center p-2">{item.dayaAki}</td>
                    <td className="text-center p-2">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        item.event === 'UNLOCK'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {item.event}
                      </span>
                    </td>
                    <td className="text-center p-2 font-mono text-sm">{item.idVendor}</td>
                    <td className="text-center p-2">{item.kecepatan}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.posisiLatitude}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.posisiLongitude}</td>
                    <td className="text-center p-2">{item.provinsi}</td>
                    <td className="text-center p-2">{item.suhu}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.noIMEI}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.noESeal}</td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
        <div className="text-sm text-gray-700">
          Menampilkan {startEntry} sampai {endEntry} dari {totalEntries} entri
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Previous
          </button>

          <div className="flex items-center space-x-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={`w-8 h-8 text-sm rounded ${
                  currentPage === page
                    ? 'bg-blue-600 text-white'
                    : 'border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {page}
              </button>
            ))}
          </div>

          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
}

function UpdateStatusDevice() {
  const [search, setSearch] = useState('');
  const [entriesPerPage, setEntriesPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Mock data sesuai dengan struktur table yang diminta
  const mockData = [
    {
      id: 1,
      perusahaan: "Perusahaan A",
      idVendor: "884GKEL637",
      noIMEI: "875298572967967922",
      status: "ACTIVE",
      token: "12356788"
    }
  ];

  const filteredData = mockData.filter(item =>
    item.perusahaan.toLowerCase().includes(search.toLowerCase()) ||
    item.idVendor.toLowerCase().includes(search.toLowerCase()) ||
    item.noIMEI.toLowerCase().includes(search.toLowerCase()) ||
    item.status.toLowerCase().includes(search.toLowerCase()) ||
    item.token.toLowerCase().includes(search.toLowerCase())
  );

  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / entriesPerPage);
  const startEntry = (currentPage - 1) * entriesPerPage + 1;
  const endEntry = Math.min(currentPage * entriesPerPage, totalEntries);

  const getStatusBadge = (status: string) => {
    return (
      <span className={`px-2 py-1 rounded text-xs font-medium ${
        status === 'ACTIVE'
          ? 'bg-green-100 text-green-800'
          : 'bg-red-100 text-red-800'
      }`}>
        {status}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Update Status Device</h1>
        <div className="w-16 h-1 bg-blue-600"></div>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Show</span>
          <select
            value={entriesPerPage}
            onChange={(e) => {
              setEntriesPerPage(parseInt(e.target.value));
              setCurrentPage(1);
            }}
            className="border border-gray-300 rounded px-2 py-1 text-sm"
          >
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
          </select>
          <span className="text-sm text-gray-700">entries per page</span>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Cari</span>
          <div className="relative">
            <input
              type="text"
              placeholder="Search..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="border border-gray-300 rounded px-3 py-1 pl-8 text-sm w-64"
            />
            <svg className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
        <div className="overflow-x-auto">
          <table className="w-full caption-bottom text-sm">
            <thead className="border-b">
              <tr className="bg-slate-50 border-b-2 border-slate-200">
                <th className="min-w-[50px] font-semibold text-slate-700 text-center p-2">No</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Perusahaan</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">ID Vendor</th>
                <th className="min-w-[180px] font-semibold text-slate-700 text-center p-2">No IMEI</th>
                <th className="min-w-[100px] font-semibold text-slate-700 text-center p-2">Status</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Token</th>
              </tr>
            </thead>
            <tbody>
              {filteredData.length === 0 ? (
                <tr>
                  <td colSpan={6} className="text-center py-8 text-gray-500">
                    {search ? 'Data tidak ditemukan' : 'Data Masih Kosong'}
                  </td>
                </tr>
              ) : (
                filteredData.map((item, index) => (
                  <tr key={item.id} className="hover:bg-slate-50 transition-colors border-b">
                    <td className="text-center p-2 font-medium">{startEntry + index}.</td>
                    <td className="text-center p-2">{item.perusahaan}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.idVendor}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.noIMEI}</td>
                    <td className="text-center p-2">{getStatusBadge(item.status)}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.token}</td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
        <div className="text-sm text-gray-700">
          Menampilkan {startEntry} sampai {endEntry} dari {totalEntries} entri
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Previous
          </button>

          <div className="flex items-center space-x-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={`w-8 h-8 text-sm rounded ${
                  currentPage === page
                    ? 'bg-blue-600 text-white'
                    : 'border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {page}
              </button>
            ))}
          </div>

          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
}

// Tracking Data Components
function TrackingStart() {
  const [search, setSearch] = useState('');
  const [entriesPerPage, setEntriesPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Mock data untuk table Start
  const mockData = [
    {
      id: 1,
      perusahaan: "Perusahaan A",
      alamatAsal: "Surabaya",
      alamatTujuan: "Banten",
      idVendor: "884GKEL637",
      jenisKontainer: "GENERAL / DRY CARGO",
      latitudeAsal: "-6.2627272.77",
      longitudeAsal: "106.7816666.67",
      latitudeTujuan: "-6.1234567.89",
      longitudeTujuan: "106.9876543.21",
      noIMEI: "875298572967967922",
      nomorESeal: "784858252",
      provinsiAsal: "Jawa Timur",
      provinsiTujuan: "Banten",
      token: "12356788"
    }
  ];

  const filteredData = mockData.filter(item =>
    item.perusahaan.toLowerCase().includes(search.toLowerCase()) ||
    item.alamatAsal.toLowerCase().includes(search.toLowerCase()) ||
    item.alamatTujuan.toLowerCase().includes(search.toLowerCase()) ||
    item.idVendor.toLowerCase().includes(search.toLowerCase())
  );

  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / entriesPerPage);
  const startEntry = (currentPage - 1) * entriesPerPage + 1;
  const endEntry = Math.min(currentPage * entriesPerPage, totalEntries);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Start</h1>
        <div className="w-16 h-1 bg-blue-600"></div>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Show</span>
          <select
            value={entriesPerPage}
            onChange={(e) => {
              setEntriesPerPage(parseInt(e.target.value));
              setCurrentPage(1);
            }}
            className="border border-gray-300 rounded px-2 py-1 text-sm"
          >
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
          </select>
          <span className="text-sm text-gray-700">entries per page</span>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Cari</span>
          <div className="relative">
            <input
              type="text"
              placeholder="Search..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="border border-gray-300 rounded px-3 py-1 pl-8 text-sm w-64"
            />
            <svg className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
        <div className="overflow-x-auto">
          <table className="w-full caption-bottom text-sm">
            <thead className="border-b">
              <tr className="bg-slate-50 border-b-2 border-slate-200">
                <th className="min-w-[50px] font-semibold text-slate-700 text-center p-2">No</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Perusahaan</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Alamat Asal</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Alamat Tujuan</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">ID Vendor</th>
                <th className="min-w-[150px] font-semibold text-slate-700 text-center p-2">Jenis Kontainer</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Latitude Asal</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Longitude Asal</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Latitude Tujuan</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Longitude Tujuan</th>
                <th className="min-w-[180px] font-semibold text-slate-700 text-center p-2">No IMEI</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Nomor E-Seal</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Provinsi Asal</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Provinsi Tujuan</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Token</th>
              </tr>
            </thead>
            <tbody>
              {filteredData.length === 0 ? (
                <tr>
                  <td colSpan={15} className="text-center py-8 text-gray-500">
                    {search ? 'Data tidak ditemukan' : 'Data Masih Kosong'}
                  </td>
                </tr>
              ) : (
                filteredData.map((item, index) => (
                  <tr key={item.id} className="hover:bg-slate-50 transition-colors border-b">
                    <td className="text-center p-2 font-medium">{startEntry + index}.</td>
                    <td className="text-center p-2">{item.perusahaan}</td>
                    <td className="text-center p-2">{item.alamatAsal}</td>
                    <td className="text-center p-2">{item.alamatTujuan}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.idVendor}</td>
                    <td className="text-center p-2">{item.jenisKontainer}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.latitudeAsal}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.longitudeAsal}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.latitudeTujuan}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.longitudeTujuan}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.noIMEI}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.nomorESeal}</td>
                    <td className="text-center p-2">{item.provinsiAsal}</td>
                    <td className="text-center p-2">{item.provinsiTujuan}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.token}</td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
        <div className="text-sm text-gray-700">
          Menampilkan {startEntry} sampai {endEntry} dari {totalEntries} entri
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Previous
          </button>

          <div className="flex items-center space-x-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={`w-8 h-8 text-sm rounded ${
                  currentPage === page
                    ? 'bg-blue-600 text-white'
                    : 'border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {page}
              </button>
            ))}
          </div>

          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
}

function TrackingStop() {
  const [search, setSearch] = useState('');
  const [entriesPerPage, setEntriesPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Mock data untuk table Stop
  const mockData = [
    {
      id: 1,
      perusahaan: "Perusahaan A",
      alamatStop: "Jakarta Utara, DKI Jakarta",
      idVendor: "884GKEL637",
      latitudeStop: "-13156771",
      longitudeStop: "7748481.11",
      noIMEI: "875298572967967922",
      nomorESeal: "784858252",
      token: "12356788"
    }
  ];

  const filteredData = mockData.filter(item =>
    item.perusahaan.toLowerCase().includes(search.toLowerCase()) ||
    item.alamatStop.toLowerCase().includes(search.toLowerCase()) ||
    item.idVendor.toLowerCase().includes(search.toLowerCase())
  );

  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / entriesPerPage);
  const startEntry = (currentPage - 1) * entriesPerPage + 1;
  const endEntry = Math.min(currentPage * entriesPerPage, totalEntries);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Stop</h1>
        <div className="w-16 h-1 bg-blue-600"></div>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Show</span>
          <select
            value={entriesPerPage}
            onChange={(e) => {
              setEntriesPerPage(parseInt(e.target.value));
              setCurrentPage(1);
            }}
            className="border border-gray-300 rounded px-2 py-1 text-sm"
          >
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
          </select>
          <span className="text-sm text-gray-700">entries per page</span>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Cari</span>
          <div className="relative">
            <input
              type="text"
              placeholder="Search..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="border border-gray-300 rounded px-3 py-1 pl-8 text-sm w-64"
            />
            <svg className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
        <div className="overflow-x-auto">
          <table className="w-full caption-bottom text-sm">
            <thead className="border-b">
              <tr className="bg-slate-50 border-b-2 border-slate-200">
                <th className="min-w-[50px] font-semibold text-slate-700 text-center p-2">No</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Perusahaan</th>
                <th className="min-w-[200px] font-semibold text-slate-700 text-center p-2">Alamat Stop</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">ID Vendor</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Latitude Stop</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Longitude Stop</th>
                <th className="min-w-[180px] font-semibold text-slate-700 text-center p-2">No IMEI</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Nomor E-Seal</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-2">Token</th>
              </tr>
            </thead>
            <tbody>
              {filteredData.length === 0 ? (
                <tr>
                  <td colSpan={9} className="text-center py-8 text-gray-500">
                    {search ? 'Data tidak ditemukan' : 'Data Masih Kosong'}
                  </td>
                </tr>
              ) : (
                filteredData.map((item, index) => (
                  <tr key={item.id} className="hover:bg-slate-50 transition-colors border-b">
                    <td className="text-center p-2 font-medium">{startEntry + index}.</td>
                    <td className="text-center p-2">{item.perusahaan}</td>
                    <td className="text-center p-2">{item.alamatStop}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.idVendor}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.latitudeStop}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.longitudeStop}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.noIMEI}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.nomorESeal}</td>
                    <td className="text-center p-2 font-mono text-sm">{item.token}</td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
        <div className="text-sm text-gray-700">
          Menampilkan {startEntry} sampai {endEntry} dari {totalEntries} entri
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Previous
          </button>

          <div className="flex items-center space-x-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={`w-8 h-8 text-sm rounded ${
                  currentPage === page
                    ? 'bg-blue-600 text-white'
                    : 'border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {page}
              </button>
            ))}
          </div>

          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
}

function TrackingStatus() {
  const [idVendor, setIdVendor] = useState('');
  const [nomorESeal, setNomorESeal] = useState('');
  const [token, setToken] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', { idVendor, nomorESeal, token });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Status</h1>
        <div className="w-16 h-1 bg-blue-600"></div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <input
              type="text"
              placeholder="ID Vendor *"
              value={idVendor}
              onChange={(e) => setIdVendor(e.target.value)}
              className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              required
            />
          </div>
          <div>
            <input
              type="text"
              placeholder="Nomor E-Seal *"
              value={nomorESeal}
              onChange={(e) => setNomorESeal(e.target.value)}
              className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              required
            />
          </div>
        </div>

        <div>
          <input
            type="text"
            placeholder="Token *"
            value={token}
            onChange={(e) => setToken(e.target.value)}
            className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
            required
          />
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">* Kolom wajib diisi</span>
          <button
            type="submit"
            className="bg-blue-600 text-white px-6 py-2 rounded text-sm hover:bg-blue-700 transition-colors"
          >
            Cari
          </button>
        </div>
      </form>

      {/* Result Table */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-900">Nomor E-Seal: -</h2>

        <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
          <table className="w-full caption-bottom text-sm">
            <thead className="border-b">
              <tr className="bg-slate-50 border-b-2 border-slate-200">
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-3">Perusahaan</th>
                <th className="min-w-[100px] font-semibold text-slate-700 text-center p-3">Start</th>
                <th className="min-w-[120px] font-semibold text-slate-700 text-center p-3">Update Position</th>
                <th className="min-w-[100px] font-semibold text-slate-700 text-center p-3">Stop</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td colSpan={4} className="text-center py-8 text-gray-500">
                  Data Masih Kosong
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

function Logs() {
  return (
    <div className="space-y-4">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Logs</h1>
        <div className="w-16 h-1 bg-blue-600"></div>
      </div>
      <div className="bg-gray-50 p-8 rounded-lg">
        <p className="text-gray-600">Halaman Logs akan segera tersedia.</p>
      </div>
    </div>
  );
}

function App() {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeMenu, setActiveMenu] = useState('tambah-data');

  // Map paths to menu IDs
  const pathToMenuId = {
    '/': 'tambah-data',
    '/tambah-data': 'tambah-data',
    '/dokumen-kepabeaan': 'dokumen-kepabeaan',
    '/update-posisi': 'update-posisi',
    '/update-status-device': 'update-status-device',
    '/logs': 'logs'
  };

  // Update active menu based on current path
  useEffect(() => {
    const menuId = pathToMenuId[location.pathname as keyof typeof pathToMenuId] || 'tambah-data';
    setActiveMenu(menuId);
  }, [location.pathname]);

  const handleMenuClick = (menuId: string) => {
    const menuToPath = {
      'tambah-data': '/tambah-data',
      'dokumen-kepabeaan': '/dokumen-kepabeaan',
      'update-posisi': '/update-posisi',
      'update-status-device': '/update-status-device',
      'logs': '/logs'
    };

    const path = menuToPath[menuId as keyof typeof menuToPath] || '/tambah-data';
    navigate(path);
  };

  return (
    <Layout activeMenu={activeMenu} onMenuClick={handleMenuClick}>
      <Routes>
        <Route path="/" element={<ESealTable />} />
        <Route path="/tambah-data" element={<ESealTable />} />
        <Route path="/dokumen-kepabeaan" element={<DokumenKepabeaan />} />
        <Route path="/update-posisi" element={<UpdatePosisi />} />
        <Route path="/update-status-device" element={<UpdateStatusDevice />} />
        <Route path="/logs" element={<Logs />} />
      </Routes>
    </Layout>
  );
}

export default App